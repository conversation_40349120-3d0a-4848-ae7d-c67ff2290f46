#!/usr/bin/env python
"""
Create sample business plan templates for testing
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from incubator.models_business_plan import BusinessPlanTemplate

def create_sample_templates():
    """Create sample business plan templates"""
    
    templates = [
        {
            'name': 'Technology Startup Template',
            'description': 'Comprehensive business plan template for technology startups',
            'industry': 'technology',
            'template_type': 'startup',
            'difficulty_level': 'intermediate',
            'estimated_time': 45,
            'sections': {
                'executive_summary': {
                    'title': 'Executive Summary',
                    'description': 'Brief overview of your technology startup',
                    'required': True,
                    'order': 1
                },
                'market_analysis': {
                    'title': 'Market Analysis',
                    'description': 'Analysis of your target market and competition',
                    'required': True,
                    'order': 2
                },
                'product_development': {
                    'title': 'Product Development',
                    'description': 'Technical specifications and development roadmap',
                    'required': True,
                    'order': 3
                },
                'financial_projections': {
                    'title': 'Financial Projections',
                    'description': 'Revenue forecasts and funding requirements',
                    'required': True,
                    'order': 4
                }
            },
            'usage_count': 150,
            'completion_rate': 85.5,
            'is_premium': False
        },
        {
            'name': 'E-commerce Business Plan',
            'description': 'Complete template for online retail businesses',
            'industry': 'ecommerce',
            'template_type': 'ecommerce',
            'difficulty_level': 'beginner',
            'estimated_time': 30,
            'sections': {
                'business_overview': {
                    'title': 'Business Overview',
                    'description': 'Overview of your e-commerce business',
                    'required': True,
                    'order': 1
                },
                'product_catalog': {
                    'title': 'Product Catalog',
                    'description': 'Description of products you will sell',
                    'required': True,
                    'order': 2
                },
                'marketing_strategy': {
                    'title': 'Marketing Strategy',
                    'description': 'How you will attract and retain customers',
                    'required': True,
                    'order': 3
                },
                'operations_plan': {
                    'title': 'Operations Plan',
                    'description': 'Logistics, fulfillment, and customer service',
                    'required': True,
                    'order': 4
                }
            },
            'usage_count': 200,
            'completion_rate': 78.2,
            'is_premium': False
        },
        {
            'name': 'Restaurant Business Plan',
            'description': 'Specialized template for restaurant and food service businesses',
            'industry': 'restaurant',
            'template_type': 'restaurant',
            'difficulty_level': 'intermediate',
            'estimated_time': 60,
            'sections': {
                'concept_overview': {
                    'title': 'Restaurant Concept',
                    'description': 'Your restaurant concept and unique value proposition',
                    'required': True,
                    'order': 1
                },
                'menu_planning': {
                    'title': 'Menu Planning',
                    'description': 'Menu items, pricing, and food costs',
                    'required': True,
                    'order': 2
                },
                'location_analysis': {
                    'title': 'Location Analysis',
                    'description': 'Site selection and demographic analysis',
                    'required': True,
                    'order': 3
                },
                'financial_planning': {
                    'title': 'Financial Planning',
                    'description': 'Startup costs, operating expenses, and revenue projections',
                    'required': True,
                    'order': 4
                }
            },
            'usage_count': 95,
            'completion_rate': 72.8,
            'is_premium': False
        },
        {
            'name': 'Consulting Services Template',
            'description': 'Professional template for consulting and service businesses',
            'industry': 'consulting',
            'template_type': 'consulting',
            'difficulty_level': 'beginner',
            'estimated_time': 25,
            'sections': {
                'service_overview': {
                    'title': 'Service Overview',
                    'description': 'Description of your consulting services',
                    'required': True,
                    'order': 1
                },
                'target_market': {
                    'title': 'Target Market',
                    'description': 'Identification of your ideal clients',
                    'required': True,
                    'order': 2
                },
                'pricing_strategy': {
                    'title': 'Pricing Strategy',
                    'description': 'How you will price your services',
                    'required': True,
                    'order': 3
                },
                'growth_plan': {
                    'title': 'Growth Plan',
                    'description': 'How you will scale your consulting business',
                    'required': True,
                    'order': 4
                }
            },
            'usage_count': 120,
            'completion_rate': 88.1,
            'is_premium': False
        },
        {
            'name': 'SaaS Startup Template',
            'description': 'Comprehensive template for Software as a Service businesses',
            'industry': 'saas',
            'template_type': 'saas',
            'difficulty_level': 'advanced',
            'estimated_time': 90,
            'sections': {
                'product_vision': {
                    'title': 'Product Vision',
                    'description': 'Your SaaS product vision and mission',
                    'required': True,
                    'order': 1
                },
                'technical_architecture': {
                    'title': 'Technical Architecture',
                    'description': 'System architecture and technology stack',
                    'required': True,
                    'order': 2
                },
                'subscription_model': {
                    'title': 'Subscription Model',
                    'description': 'Pricing tiers and subscription strategy',
                    'required': True,
                    'order': 3
                },
                'customer_acquisition': {
                    'title': 'Customer Acquisition',
                    'description': 'How you will acquire and retain customers',
                    'required': True,
                    'order': 4
                }
            },
            'usage_count': 75,
            'completion_rate': 91.3,
            'is_premium': True
        }
    ]
    
    created_count = 0
    for template_data in templates:
        template, created = BusinessPlanTemplate.objects.get_or_create(
            name=template_data['name'],
            defaults=template_data
        )
        if created:
            created_count += 1
            print(f"✅ Created template: {template.name}")
        else:
            print(f"⚠️  Template already exists: {template.name}")
    
    print(f"\n🎉 Created {created_count} new templates!")
    print(f"📊 Total templates in database: {BusinessPlanTemplate.objects.count()}")

if __name__ == '__main__':
    create_sample_templates()
